'use client'

import { useState, useEffect, useCallback } from 'react'
import Link from 'next/link'
import { ProtectedRoute } from '@/components/auth/ProtectedRoute'
import { usePermissions } from '@/components/providers/AuthProvider'
import { Button } from '@/components/ui/Button'
import { DataTable } from '@/components/ui/DataTable'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card'
import { paymentService } from '@/lib/services/paymentService'
import { customerService } from '@/lib/services/customerService'
import { formatCurrency, formatDate } from '@/utils'
import type { Payment, Customer, PaymentFilters, PaginationParams } from '@/types'

function PaymentsContent() {
  const { canPerformAction } = usePermissions()
  const [payments, setPayments] = useState<Payment[]>([])
  const [customers, setCustomers] = useState<Customer[]>([])
  const [loading, setLoading] = useState(true)
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 0,
  })
  const [filters, setFilters] = useState<PaymentFilters>({})
  const [error, setError] = useState('')
  const [stats, setStats] = useState({
    total_payments: 0,
    total_payment_amount: 0,
    today_payments: 0,
    today_payment_amount: 0,
    this_month_payments: 0,
    this_month_payment_amount: 0,
    average_payment: 0,
  })

  const paymentMethods = paymentService.getPaymentMethods()

  const loadPayments = useCallback(async () => {
    try {
      setLoading(true)
      const params: PaginationParams & PaymentFilters = {
        page: pagination.page,
        limit: pagination.limit,
        ...filters,
      }

      const response = await paymentService.getPayments(params)
      setPayments(response.data)
      setPagination(response.pagination)
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to load payments')
      console.error('Failed to load payments:', error)
    } finally {
      setLoading(false)
    }
  }, [pagination.page, pagination.limit, filters])

  useEffect(() => {
    loadPayments()
    loadCustomers()
    loadStats()
  }, [loadPayments])

  const loadCustomers = async () => {
    try {
      const response = await customerService.getCustomers({ page: 1, limit: 1000, is_active: true })
      setCustomers(response.data)
    } catch (error) {
      console.error('Failed to load customers:', error)
    }
  }

  const loadStats = async () => {
    try {
      const statsData = await paymentService.getPaymentStats()
      setStats(statsData)
    } catch (error) {
      console.error('Failed to load payment stats:', error)
    }
  }

  const handleSearch = (search: string) => {
    setFilters((prev) => ({ ...prev, search }))
    setPagination((prev) => ({ ...prev, page: 1 }))
  }

  const handleSort = (column: string, direction: 'asc' | 'desc') => {
    setFilters((prev) => ({ ...prev, sort_by: column, sort_order: direction }))
  }

  const handlePageChange = (page: number) => {
    setPagination((prev) => ({ ...prev, page }))
  }

  const handleDeletePayment = async (payment: Payment) => {
    if (!canPerformAction('delete_payment')) {
      alert('You do not have permission to delete payments')
      return
    }

    if (
      !confirm(
        `Are you sure you want to delete this payment of ${formatCurrency(payment.amount_paid, 'PHP')} from "${payment.customer?.full_name}"?`
      )
    ) {
      return
    }

    try {
      await paymentService.deletePayment(payment.id)
      loadPayments() // Reload the list
      loadStats() // Reload stats
    } catch (error) {
      alert(error instanceof Error ? error.message : 'Failed to delete payment')
    }
  }

  const columns = [
    {
      key: 'customer',
      header: 'Customer',
      sortable: true,
      render: (payment: Payment) => (
        <div>
          <div className='font-medium text-gray-900'>{payment.customer?.full_name}</div>
          {payment.customer?.phone && (
            <div className='text-sm text-gray-500'>{payment.customer.phone}</div>
          )}
        </div>
      ),
    },
    {
      key: 'amount_paid',
      header: 'Amount Paid',
      sortable: true,
      render: (payment: Payment) => (
        <span className='font-medium text-green-600'>
          {formatCurrency(payment.amount_paid, 'PHP')}
        </span>
      ),
    },
    {
      key: 'payment_method',
      header: 'Payment Method',
      render: (payment: Payment) => (
        <span className='inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 capitalize'>
          {payment.payment_method.replace('_', ' ')}
        </span>
      ),
    },
    {
      key: 'payment_date',
      header: 'Payment Date',
      sortable: true,
      render: (payment: Payment) => (
        <span className='text-sm text-gray-900'>{formatDate(payment.payment_date)}</span>
      ),
    },
    {
      key: 'recorded_by',
      header: 'Recorded By',
      render: (payment: Payment) => (
        <span className='text-sm text-gray-600'>{payment.recorded_by || '-'}</span>
      ),
    },
    {
      key: 'notes',
      header: 'Notes',
      render: (payment: Payment) => (
        <span className='text-sm text-gray-600'>
          {payment.notes
            ? payment.notes.length > 30
              ? `${payment.notes.substring(0, 30)}...`
              : payment.notes
            : '-'}
        </span>
      ),
    },
  ]

  const renderActions = (payment: Payment) => (
    <div className='flex items-center space-x-2'>
      <Link href={`/payments/${payment.id}`}>
        <Button variant='outline' size='sm'>
          View
        </Button>
      </Link>
      {canPerformAction('edit_payment') && (
        <Link href={`/payments/${payment.id}/edit`}>
          <Button variant='outline' size='sm'>
            Edit
          </Button>
        </Link>
      )}
      {canPerformAction('delete_payment') && (
        <Button
          variant='outline'
          size='sm'
          onClick={() => handleDeletePayment(payment)}
          className='text-red-600 hover:text-red-700'
        >
          Delete
        </Button>
      )}
    </div>
  )

  return (
    <div className='container mx-auto px-4 py-8'>
      <div className='flex justify-between items-center mb-8'>
        <div>
          <h1 className='text-3xl font-bold text-gray-900'>Payments</h1>
          <p className='text-gray-600 mt-2'>Track customer payments and debt reductions</p>
        </div>
        {canPerformAction('process_payment') && (
          <Link href='/payments/new'>
            <Button>Process Payment</Button>
          </Link>
        )}
      </div>

      {/* Statistics Cards */}
      <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8'>
        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>Total Payments</CardTitle>
            <svg
              className='h-4 w-4 text-muted-foreground'
              fill='none'
              viewBox='0 0 24 24'
              stroke='currentColor'
            >
              <path
                strokeLinecap='round'
                strokeLinejoin='round'
                strokeWidth={2}
                d='M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z'
              />
            </svg>
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>{stats.total_payments}</div>
            <p className='text-xs text-muted-foreground'>All time payments</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>Total Amount</CardTitle>
            <svg
              className='h-4 w-4 text-muted-foreground'
              fill='none'
              viewBox='0 0 24 24'
              stroke='currentColor'
            >
              <path
                strokeLinecap='round'
                strokeLinejoin='round'
                strokeWidth={2}
                d='M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1'
              />
            </svg>
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold text-green-600'>
              {formatCurrency(stats.total_payment_amount, 'PHP')}
            </div>
            <p className='text-xs text-muted-foreground'>Total collected</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>Today&apos;s Payments</CardTitle>
            <svg
              className='h-4 w-4 text-muted-foreground'
              fill='none'
              viewBox='0 0 24 24'
              stroke='currentColor'
            >
              <path
                strokeLinecap='round'
                strokeLinejoin='round'
                strokeWidth={2}
                d='M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z'
              />
            </svg>
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>{stats.today_payments}</div>
            <p className='text-xs text-muted-foreground'>
              {formatCurrency(stats.today_payment_amount, 'PHP')}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>Average Payment</CardTitle>
            <svg
              className='h-4 w-4 text-muted-foreground'
              fill='none'
              viewBox='0 0 24 24'
              stroke='currentColor'
            >
              <path
                strokeLinecap='round'
                strokeLinejoin='round'
                strokeWidth={2}
                d='M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z'
              />
            </svg>
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>{formatCurrency(stats.average_payment, 'PHP')}</div>
            <p className='text-xs text-muted-foreground'>Per transaction</p>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card className='mb-6'>
        <CardHeader>
          <CardTitle>Filters</CardTitle>
          <CardDescription>Filter payments by customer, method, and date</CardDescription>
        </CardHeader>
        <CardContent>
          <div className='grid grid-cols-1 md:grid-cols-4 gap-4'>
            <div>
              <label className='block text-sm font-medium text-gray-700 mb-1'>Customer</label>
              <select
                value={filters.customer_id || ''}
                onChange={(e) =>
                  setFilters((prev) => ({ ...prev, customer_id: e.target.value || undefined }))
                }
                className='w-full h-10 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'
              >
                <option value=''>All Customers</option>
                {customers.map((customer) => (
                  <option key={customer.id} value={customer.id}>
                    {customer.full_name}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className='block text-sm font-medium text-gray-700 mb-1'>Payment Method</label>
              <select
                value={filters.payment_method || ''}
                onChange={(e) =>
                  setFilters((prev) => ({ ...prev, payment_method: e.target.value || undefined }))
                }
                className='w-full h-10 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'
              >
                <option value=''>All Methods</option>
                {paymentMethods.map((method) => (
                  <option key={method.value} value={method.value}>
                    {method.label}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className='block text-sm font-medium text-gray-700 mb-1'>Date From</label>
              <input
                type='date'
                value={filters.date_from || ''}
                onChange={(e) =>
                  setFilters((prev) => ({ ...prev, date_from: e.target.value || undefined }))
                }
                className='w-full h-10 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'
              />
            </div>

            <div>
              <label className='block text-sm font-medium text-gray-700 mb-1'>Date To</label>
              <input
                type='date'
                value={filters.date_to || ''}
                onChange={(e) =>
                  setFilters((prev) => ({ ...prev, date_to: e.target.value || undefined }))
                }
                className='w-full h-10 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'
              />
            </div>
          </div>

          <div className='mt-4'>
            <Button
              variant='outline'
              onClick={() => {
                setFilters({})
                setPagination((prev) => ({ ...prev, page: 1 }))
              }}
            >
              Clear Filters
            </Button>
          </div>
        </CardContent>
      </Card>

      {error && (
        <div className='bg-red-50 border border-red-200 rounded-md p-4 mb-6'>
          <p className='text-sm text-red-600'>{error}</p>
        </div>
      )}

      <DataTable
        data={payments}
        columns={columns}
        loading={loading}
        pagination={pagination}
        onPageChange={handlePageChange}
        onSearch={handleSearch}
        onSort={handleSort}
        searchPlaceholder='Search payments...'
        emptyMessage='No payments found'
        actions={renderActions}
      />
    </div>
  )
}

export default function PaymentsPage() {
  return (
    <ProtectedRoute requiredPermission='view_analytics'>
      <PaymentsContent />
    </ProtectedRoute>
  )
}
