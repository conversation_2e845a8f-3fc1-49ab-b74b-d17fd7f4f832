'use client'

import { useState } from 'react'
import { Button } from './Button'
import { Input } from './Input'
import { cn } from '@/utils/cn'

interface Column<T> {
  key: keyof T | string
  header: string
  render?: (item: T) => React.ReactNode
  sortable?: boolean
  className?: string
}

interface DataTableProps<T> {
  data: T[]
  columns: Column<T>[]
  loading?: boolean
  pagination?: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
  onPageChange?: (page: number) => void
  onSearch?: (search: string) => void
  onSort?: (column: string, direction: 'asc' | 'desc') => void
  searchPlaceholder?: string
  emptyMessage?: string
  actions?: (item: T) => React.ReactNode
}

export function DataTable<T = Record<string, unknown>>({
  data,
  columns,
  loading = false,
  pagination,
  onPageChange,
  onSearch,
  onSort,
  searchPlaceholder = 'Search...',
  emptyMessage = 'No data found',
  actions,
}: DataTableProps<T>) {
  const [search, setSearch] = useState('')
  const [sortColumn, setSortColumn] = useState<string>('')
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc')

  const handleSearch = (value: string) => {
    setSearch(value)
    onSearch?.(value)
  }

  const handleSort = (column: string) => {
    if (!onSort) return

    let direction: 'asc' | 'desc' = 'asc'
    if (sortColumn === column && sortDirection === 'asc') {
      direction = 'desc'
    }

    setSortColumn(column)
    setSortDirection(direction)
    onSort(column, direction)
  }

  const renderCell = (item: T, column: Column<T>) => {
    if (column.render) {
      return column.render(item)
    }

    const value = item[column.key as keyof T]
    return value?.toString() || '-'
  }

  const renderPagination = () => {
    if (!pagination || !onPageChange) return null

    const { page, totalPages } = pagination
    const pages = []
    const maxVisiblePages = 5

    let startPage = Math.max(1, page - Math.floor(maxVisiblePages / 2))
    const endPage = Math.min(totalPages, startPage + maxVisiblePages - 1)

    if (endPage - startPage + 1 < maxVisiblePages) {
      startPage = Math.max(1, endPage - maxVisiblePages + 1)
    }

    for (let i = startPage; i <= endPage; i++) {
      pages.push(i)
    }

    return (
      <div className='flex items-center justify-between px-4 py-3 bg-white border-t border-gray-200'>
        <div className='flex items-center text-sm text-gray-700'>
          <span>
            Showing {(page - 1) * pagination.limit + 1} to{' '}
            {Math.min(page * pagination.limit, pagination.total)} of {pagination.total} results
          </span>
        </div>

        <div className='flex items-center space-x-2'>
          <Button
            variant='outline'
            size='sm'
            onClick={() => onPageChange(page - 1)}
            disabled={page <= 1}
          >
            Previous
          </Button>

          {pages.map((pageNum) => (
            <Button
              key={pageNum}
              variant={pageNum === page ? 'primary' : 'outline'}
              size='sm'
              onClick={() => onPageChange(pageNum)}
            >
              {pageNum}
            </Button>
          ))}

          <Button
            variant='outline'
            size='sm'
            onClick={() => onPageChange(page + 1)}
            disabled={page >= totalPages}
          >
            Next
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className='bg-white shadow rounded-lg overflow-hidden'>
      {/* Search Bar */}
      {onSearch && (
        <div className='p-4 border-b border-gray-200'>
          <Input
            placeholder={searchPlaceholder}
            value={search}
            onChange={(e) => handleSearch(e.target.value)}
            className='max-w-sm'
          />
        </div>
      )}

      {/* Table */}
      <div className='overflow-x-auto'>
        <table className='min-w-full divide-y divide-gray-200'>
          <thead className='bg-gray-50'>
            <tr>
              {columns.map((column, index) => (
                <th
                  key={index}
                  className={cn(
                    'px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider',
                    column.sortable && 'cursor-pointer hover:bg-gray-100',
                    column.className
                  )}
                  onClick={() => column.sortable && handleSort(column.key as string)}
                >
                  <div className='flex items-center space-x-1'>
                    <span>{column.header}</span>
                    {column.sortable && (
                      <svg
                        className={cn(
                          'w-4 h-4',
                          sortColumn === column.key ? 'text-gray-900' : 'text-gray-400'
                        )}
                        fill='none'
                        stroke='currentColor'
                        viewBox='0 0 24 24'
                      >
                        {sortColumn === column.key && sortDirection === 'desc' ? (
                          <path
                            strokeLinecap='round'
                            strokeLinejoin='round'
                            strokeWidth={2}
                            d='M19 9l-7 7-7-7'
                          />
                        ) : (
                          <path
                            strokeLinecap='round'
                            strokeLinejoin='round'
                            strokeWidth={2}
                            d='M5 15l7-7 7 7'
                          />
                        )}
                      </svg>
                    )}
                  </div>
                </th>
              ))}
              {actions && (
                <th className='px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider'>
                  Actions
                </th>
              )}
            </tr>
          </thead>
          <tbody className='bg-white divide-y divide-gray-200'>
            {loading ? (
              <tr>
                <td colSpan={columns.length + (actions ? 1 : 0)} className='px-6 py-12 text-center'>
                  <div className='flex items-center justify-center'>
                    <div className='animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600' />
                    <span className='ml-2 text-gray-500'>Loading...</span>
                  </div>
                </td>
              </tr>
            ) : data.length === 0 ? (
              <tr>
                <td
                  colSpan={columns.length + (actions ? 1 : 0)}
                  className='px-6 py-12 text-center text-gray-500'
                >
                  {emptyMessage}
                </td>
              </tr>
            ) : (
              data.map((item, index) => (
                <tr key={index} className='hover:bg-gray-50'>
                  {columns.map((column, colIndex) => (
                    <td
                      key={colIndex}
                      className={cn(
                        'px-6 py-4 whitespace-nowrap text-sm text-gray-900',
                        column.className
                      )}
                    >
                      {renderCell(item, column)}
                    </td>
                  ))}
                  {actions && (
                    <td className='px-6 py-4 whitespace-nowrap text-right text-sm font-medium'>
                      {actions(item)}
                    </td>
                  )}
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      {renderPagination()}
    </div>
  )
}
