/**
 * Centralized Error Handling System
 *
 * This module provides comprehensive error handling utilities for the application,
 * including error classification, logging, and user-friendly error messages.
 */

export enum ErrorType {
  CONFIGURATION = 'CONFIGURATION',
  SUPABASE = 'SUPABASE',
  AUTHENTICATION = 'AUTHENTICATION',
  AUTHORIZATION = 'AUTH<PERSON>IZATION',
  VALIDATION = 'VALIDATION',
  NETWORK = 'NETWORK',
  UNKNOWN = 'UNKNOWN',
}

export interface AppError {
  type: ErrorType
  message: string
  originalError?: Error
  context?: Record<string, unknown>
  timestamp: Date
  userMessage: string
  actionable: boolean
}

/**
 * Creates a standardized application error
 */
export function createAppError(
  type: ErrorType,
  message: string,
  originalError?: Error,
  context?: Record<string, unknown>
): AppError {
  return {
    type,
    message,
    originalError,
    context,
    timestamp: new Date(),
    userMessage: getUserFriendlyMessage(type, message),
    actionable: isActionableError(type),
  }
}

/**
 * Converts technical error messages to user-friendly messages
 */
function getUserFriendlyMessage(type: ErrorType, message: string): string {
  switch (type) {
    case ErrorType.CONFIGURATION:
      return 'There seems to be a configuration issue. Please check your environment settings.'

    case ErrorType.SUPABASE:
      if (message.includes('supabaseKey is required')) {
        return 'Database connection failed. Please check your configuration and try again.'
      }
      if (message.includes('Invalid URL')) {
        return 'Database URL is invalid. Please check your configuration.'
      }
      return 'Database error occurred. Please try again later.'

    case ErrorType.AUTHENTICATION:
      return 'Authentication failed. Please check your credentials and try again.'

    case ErrorType.AUTHORIZATION:
      return 'You do not have permission to perform this action.'

    case ErrorType.VALIDATION:
      return 'Please check your input and try again.'

    case ErrorType.NETWORK:
      return 'Network error occurred. Please check your connection and try again.'

    default:
      return 'An unexpected error occurred. Please try again later.'
  }
}

/**
 * Determines if an error can be resolved by user action
 */
function isActionableError(type: ErrorType): boolean {
  return [ErrorType.CONFIGURATION, ErrorType.AUTHENTICATION, ErrorType.VALIDATION].includes(type)
}

/**
 * Classifies errors based on their characteristics
 */
export function classifyError(error: Error): ErrorType {
  const message = error.message.toLowerCase()

  if (message.includes('supabase') || message.includes('database')) {
    return ErrorType.SUPABASE
  }

  if (
    message.includes('environment') ||
    message.includes('configuration') ||
    message.includes('missing')
  ) {
    return ErrorType.CONFIGURATION
  }

  if (message.includes('auth') || message.includes('login') || message.includes('credential')) {
    return ErrorType.AUTHENTICATION
  }

  if (
    message.includes('permission') ||
    message.includes('unauthorized') ||
    message.includes('forbidden')
  ) {
    return ErrorType.AUTHORIZATION
  }

  if (
    message.includes('validation') ||
    message.includes('invalid') ||
    message.includes('required')
  ) {
    return ErrorType.VALIDATION
  }

  if (message.includes('network') || message.includes('fetch') || message.includes('connection')) {
    return ErrorType.NETWORK
  }

  return ErrorType.UNKNOWN
}

/**
 * Handles errors with appropriate logging and user feedback
 */
export function handleError(error: Error, context?: Record<string, unknown>): AppError {
  const errorType = classifyError(error)
  const appError = createAppError(errorType, error.message, error, context)

  // Log error for debugging
  logError(appError)

  return appError
}

/**
 * Logs errors with appropriate level based on type
 */
function logError(appError: AppError): void {
  const logData = {
    type: appError.type,
    message: appError.message,
    timestamp: appError.timestamp,
    context: appError.context,
    stack: appError.originalError?.stack,
  }

  switch (appError.type) {
    case ErrorType.CONFIGURATION:
    case ErrorType.SUPABASE:
      console.error('🔧 Configuration Error:', logData)
      break

    case ErrorType.AUTHENTICATION:
    case ErrorType.AUTHORIZATION:
      console.warn('🔐 Auth Error:', logData)
      break

    case ErrorType.VALIDATION:
      console.warn('📝 Validation Error:', logData)
      break

    case ErrorType.NETWORK:
      console.warn('🌐 Network Error:', logData)
      break

    default:
      console.error('❌ Unknown Error:', logData)
  }
}

/**
 * React hook for error handling
 */
export function useErrorHandler() {
  const handleErrorHook = (error: Error, context?: Record<string, unknown>) => {
    return handleError(error, context)
  }

  const handleAsyncError = async (
    asyncFn: () => Promise<unknown>,
    context?: Record<string, unknown>
  ) => {
    try {
      return await asyncFn()
    } catch (error) {
      const appError = handleError(error as Error, context)
      throw appError
    }
  }

  return { handleError: handleErrorHook, handleAsyncError }
}

/**
 * Get error title for display
 */
export function getErrorTitle(errorType: ErrorType): string {
  switch (errorType) {
    case ErrorType.CONFIGURATION:
      return 'Configuration Error'
    case ErrorType.SUPABASE:
      return 'Database Error'
    case ErrorType.AUTHENTICATION:
      return 'Authentication Error'
    case ErrorType.AUTHORIZATION:
      return 'Permission Error'
    case ErrorType.VALIDATION:
      return 'Validation Error'
    case ErrorType.NETWORK:
      return 'Network Error'
    default:
      return 'Application Error'
  }
}

/**
 * Get actionable advice for error types
 */
export function getActionableAdvice(errorType: ErrorType): string {
  switch (errorType) {
    case ErrorType.CONFIGURATION:
      return 'Check your .env.local file and ensure all required environment variables are set correctly.'
    case ErrorType.AUTHENTICATION:
      return 'Please check your login credentials and try again.'
    case ErrorType.VALIDATION:
      return 'Please review your input and ensure all required fields are filled correctly.'
    default:
      return 'Please try refreshing the page or contact support if the problem persists.'
  }
}
