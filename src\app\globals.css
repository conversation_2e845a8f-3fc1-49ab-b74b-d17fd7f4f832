@import 'tailwindcss';

/* Professional Design System - Facebook/YouTube Inspired */
:root {
  /* Primary Brand Colors */
  --primary-50: #eff6ff;
  --primary-100: #dbeafe;
  --primary-200: #bfdbfe;
  --primary-300: #93c5fd;
  --primary-400: #60a5fa;
  --primary-500: #3b82f6;
  --primary-600: #2563eb;
  --primary-700: #1d4ed8;
  --primary-800: #1e40af;
  --primary-900: #1e3a8a;

  /* Neutral Colors */
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --gray-900: #111827;

  /* Success Colors */
  --success-50: #ecfdf5;
  --success-100: #d1fae5;
  --success-500: #10b981;
  --success-600: #059669;

  /* Warning Colors */
  --warning-50: #fffbeb;
  --warning-100: #fef3c7;
  --warning-500: #f59e0b;
  --warning-600: #d97706;

  /* Error Colors */
  --error-50: #fef2f2;
  --error-100: #fee2e2;
  --error-500: #ef4444;
  --error-600: #dc2626;

  /* Background and Foreground */
  --background: #f9fafb;
  --foreground: #111827;
  --card-background: #ffffff;

  /* Font Variables */
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

/* Base Styles */
body {
  background: var(--background);
  color: var(--foreground);
  font-family:
    var(--font-sans),
    -apple-system,
    BlinkMacSystemFont,
    'Segoe UI',
    Roboto,
    sans-serif;
  font-feature-settings:
    'rlig' 1,
    'calt' 1;
  line-height: 1.6;
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Focus styles */
*:focus-visible {
  outline: none;
  box-shadow:
    0 0 0 2px var(--primary-500),
    0 0 0 4px rgba(59, 130, 246, 0.1);
}

/* Professional Component Styles */
.card-modern {
  background: var(--card-background);
  border-radius: 12px;
  border: 1px solid var(--gray-200);
  box-shadow:
    0 1px 3px 0 rgba(0, 0, 0, 0.1),
    0 1px 2px 0 rgba(0, 0, 0, 0.06);
  transition: all 0.2s ease-in-out;
}

.card-modern:hover {
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
  transform: translateY(-1px);
}

.card-elevated {
  background: var(--card-background);
  border-radius: 16px;
  border: none;
  box-shadow:
    0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease-in-out;
}

.card-elevated:hover {
  box-shadow:
    0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04);
  transform: translateY(-2px);
}

/* Professional Button Styles */
.btn-primary {
  background: linear-gradient(135deg, var(--primary-600), var(--primary-700));
  color: white;
  font-weight: 500;
  padding: 10px 16px;
  border-radius: 8px;
  border: none;
  transition: all 0.2s ease-in-out;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  cursor: pointer;
}

.btn-primary:hover {
  background: linear-gradient(135deg, var(--primary-700), var(--primary-800));
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
  transform: translateY(-1px);
}

.btn-secondary {
  background: var(--gray-100);
  color: var(--gray-700);
  font-weight: 500;
  padding: 10px 16px;
  border-radius: 8px;
  border: 1px solid var(--gray-300);
  transition: all 0.2s ease-in-out;
  cursor: pointer;
}

.btn-secondary:hover {
  background: var(--gray-200);
  border-color: var(--gray-400);
  transform: translateY(-1px);
}

/* Professional Navigation */
.nav-modern {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(12px);
  border-bottom: 1px solid var(--gray-200);
  box-shadow:
    0 1px 3px 0 rgba(0, 0, 0, 0.1),
    0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

.nav-link {
  color: var(--gray-600);
  font-weight: 500;
  padding: 8px 12px;
  border-radius: 6px;
  transition: all 0.2s ease-in-out;
  text-decoration: none;
}

.nav-link:hover {
  color: var(--gray-900);
  background: var(--gray-100);
}

.nav-link-active {
  color: var(--primary-600);
  background: var(--primary-50);
  font-weight: 600;
}

/* Professional Form Styles */
.form-input {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid var(--gray-300);
  border-radius: 8px;
  background: white;
  transition: all 0.2s ease-in-out;
  font-size: 14px;
}

.form-input:focus {
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  outline: none;
}

.form-label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: var(--gray-700);
  margin-bottom: 8px;
}

/* Professional Table Styles */
.table-modern {
  width: 100%;
  background: var(--card-background);
  border-radius: 12px;
  border: 1px solid var(--gray-200);
  overflow: hidden;
  box-shadow:
    0 1px 3px 0 rgba(0, 0, 0, 0.1),
    0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

.table-header {
  background: var(--gray-50);
  border-bottom: 1px solid var(--gray-200);
}

.table-row {
  border-bottom: 1px solid var(--gray-100);
  transition: background-color 0.15s ease-in-out;
}

.table-row:hover {
  background: var(--gray-50);
}

.table-cell {
  padding: 16px 24px;
  font-size: 14px;
  color: var(--gray-900);
}

.table-header-cell {
  padding: 12px 24px;
  text-align: left;
  font-size: 12px;
  font-weight: 600;
  color: var(--gray-500);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* Professional Metric Cards */
.metric-card {
  background: var(--card-background);
  border-radius: 12px;
  padding: 24px;
  border: 1px solid var(--gray-200);
  box-shadow:
    0 1px 3px 0 rgba(0, 0, 0, 0.1),
    0 1px 2px 0 rgba(0, 0, 0, 0.06);
  transition: all 0.2s ease-in-out;
}

.metric-card:hover {
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
  transform: translateY(-1px);
}

.metric-card-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  margin-bottom: 16px;
}

.metric-card-icon.blue {
  background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
}

.metric-card-icon.green {
  background: linear-gradient(135deg, var(--success-500), var(--success-600));
}

.metric-card-icon.red {
  background: linear-gradient(135deg, var(--error-500), var(--error-600));
}

.metric-card-icon.yellow {
  background: linear-gradient(135deg, var(--warning-500), var(--warning-600));
}

.metric-card-icon.purple {
  background: linear-gradient(135deg, #8b5cf6, #7c3aed);
}

/* Professional Loading States */
.skeleton {
  background: linear-gradient(90deg, var(--gray-200) 25%, var(--gray-300) 50%, var(--gray-200) 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
  border-radius: 4px;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

.loading-spinner {
  border: 2px solid var(--gray-300);
  border-top: 2px solid var(--primary-600);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Professional Status Badges */
.badge-success {
  display: inline-flex;
  align-items: center;
  padding: 4px 10px;
  border-radius: 9999px;
  font-size: 12px;
  font-weight: 500;
  background: var(--success-100);
  color: var(--success-600);
}

.badge-warning {
  display: inline-flex;
  align-items: center;
  padding: 4px 10px;
  border-radius: 9999px;
  font-size: 12px;
  font-weight: 500;
  background: var(--warning-100);
  color: var(--warning-600);
}

.badge-error {
  display: inline-flex;
  align-items: center;
  padding: 4px 10px;
  border-radius: 9999px;
  font-size: 12px;
  font-weight: 500;
  background: var(--error-100);
  color: var(--error-600);
}

.badge-neutral {
  display: inline-flex;
  align-items: center;
  padding: 4px 10px;
  border-radius: 9999px;
  font-size: 12px;
  font-weight: 500;
  background: var(--gray-100);
  color: var(--gray-600);
}

/* Utility Classes */
.text-gradient {
  background: linear-gradient(135deg, var(--primary-600), var(--primary-800));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.bg-gradient-primary {
  background: linear-gradient(135deg, var(--primary-600), var(--primary-700));
}

.shadow-glow {
  box-shadow: 0 0 20px rgba(59, 130, 246, 0.15);
}

.backdrop-blur-glass {
  backdrop-filter: blur(12px);
  background: rgba(255, 255, 255, 0.8);
}
