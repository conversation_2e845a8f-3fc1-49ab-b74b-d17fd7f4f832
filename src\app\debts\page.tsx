'use client'

import { useState, useEffect, useCallback } from 'react'
import Link from 'next/link'
import { ProtectedRoute } from '@/components/auth/ProtectedRoute'
import { usePermissions } from '@/components/providers/AuthProvider'
import { Button } from '@/components/ui/Button'
import { DataTable } from '@/components/ui/DataTable'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card'
import { debtService } from '@/lib/services/debtService'
import { customerService } from '@/lib/services/customerService'
import { formatCurrency, formatDate } from '@/utils'
import type { DebtTransaction, Customer, DebtFilters, PaginationParams } from '@/types'

function DebtsContent() {
  const { canPerformAction } = usePermissions()
  const [debts, setDebts] = useState<DebtTransaction[]>([])
  const [customers, setCustomers] = useState<Customer[]>([])
  const [loading, setLoading] = useState(true)
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 0,
  })
  const [filters, setFilters] = useState<DebtFilters>({})
  const [error, setError] = useState('')
  const [stats, setStats] = useState({
    total_debts: 0,
    unpaid_debts: 0,
    paid_debts: 0,
    total_debt_amount: 0,
    unpaid_debt_amount: 0,
    paid_debt_amount: 0,
    payment_rate: 0,
  })

  const loadDebts = useCallback(async () => {
    try {
      setLoading(true)
      const params: PaginationParams & DebtFilters = {
        page: pagination.page,
        limit: pagination.limit,
        ...filters,
      }

      const response = await debtService.getDebts(params)
      setDebts(response.data)
      setPagination(response.pagination)
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to load debts')
      console.error('Failed to load debts:', error)
    } finally {
      setLoading(false)
    }
  }, [pagination.page, pagination.limit, filters])

  const loadCustomers = useCallback(async () => {
    try {
      const response = await customerService.getCustomers({ page: 1, limit: 1000, is_active: true })
      setCustomers(response.data)
    } catch (error) {
      console.error('Failed to load customers:', error)
    }
  }, [])

  const loadStats = useCallback(async () => {
    try {
      const statsData = await debtService.getDebtStats()
      setStats(statsData)
    } catch (error) {
      console.error('Failed to load debt stats:', error)
    }
  }, [])

  useEffect(() => {
    loadDebts()
    loadCustomers()
    loadStats()
  }, [loadDebts, loadCustomers, loadStats])

  const handleSearch = (search: string) => {
    setFilters((prev) => ({ ...prev, search }))
    setPagination((prev) => ({ ...prev, page: 1 }))
  }

  const handleSort = (column: string, direction: 'asc' | 'desc') => {
    setFilters((prev) => ({ ...prev, sort_by: column, sort_order: direction }))
  }

  const handlePageChange = (page: number) => {
    setPagination((prev) => ({ ...prev, page }))
  }

  const handleDeleteDebt = async (debt: DebtTransaction) => {
    if (!canPerformAction('delete_debt')) {
      alert('You do not have permission to delete debt transactions')
      return
    }

    if (debt.remaining_balance !== debt.total_amount) {
      alert('Cannot delete debt transaction with payments. Please contact administrator.')
      return
    }

    if (
      !confirm(
        `Are you sure you want to delete this debt transaction for "${debt.customer?.full_name}"?`
      )
    ) {
      return
    }

    try {
      await debtService.deleteDebt(debt.id)
      loadDebts() // Reload the list
      loadStats() // Reload stats
    } catch (error) {
      alert(error instanceof Error ? error.message : 'Failed to delete debt transaction')
    }
  }

  const columns = [
    {
      key: 'customer',
      header: 'Customer',
      sortable: true,
      render: (debt: DebtTransaction) => (
        <div>
          <div className='font-medium text-gray-900'>{debt.customer?.full_name}</div>
          {debt.customer?.phone && (
            <div className='text-sm text-gray-500'>{debt.customer.phone}</div>
          )}
        </div>
      ),
    },
    {
      key: 'product_name',
      header: 'Product',
      sortable: true,
      render: (debt: DebtTransaction) => (
        <div>
          <div className='font-medium text-gray-900'>{debt.product_name}</div>
          <div className='text-sm text-gray-500'>
            Qty: {debt.quantity} × {formatCurrency(debt.product_price, 'PHP')}
          </div>
        </div>
      ),
    },
    {
      key: 'total_amount',
      header: 'Total Amount',
      sortable: true,
      render: (debt: DebtTransaction) => (
        <span className='font-medium'>{formatCurrency(debt.total_amount, 'PHP')}</span>
      ),
    },
    {
      key: 'remaining_balance',
      header: 'Remaining Balance',
      sortable: true,
      render: (debt: DebtTransaction) => (
        <span
          className={`font-medium ${
            debt.remaining_balance > 0 ? 'text-red-600' : 'text-green-600'
          }`}
        >
          {formatCurrency(debt.remaining_balance, 'PHP')}
        </span>
      ),
    },
    {
      key: 'debt_date',
      header: 'Date',
      sortable: true,
      render: (debt: DebtTransaction) => (
        <span className='text-sm text-gray-900'>{formatDate(debt.debt_date)}</span>
      ),
    },
    {
      key: 'is_fully_paid',
      header: 'Status',
      render: (debt: DebtTransaction) => (
        <span
          className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
            debt.is_fully_paid ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
          }`}
        >
          {debt.is_fully_paid ? 'Paid' : 'Unpaid'}
        </span>
      ),
    },
  ]

  const renderActions = (debt: DebtTransaction) => (
    <div className='flex items-center space-x-2'>
      <Link href={`/debts/${debt.id}`}>
        <Button variant='outline' size='sm'>
          View
        </Button>
      </Link>
      {canPerformAction('process_payment') && !debt.is_fully_paid && (
        <Link href={`/payments/new?debt_id=${debt.id}`}>
          <Button size='sm'>Pay</Button>
        </Link>
      )}
      {canPerformAction('edit_debt') && (
        <Link href={`/debts/${debt.id}/edit`}>
          <Button variant='outline' size='sm'>
            Edit
          </Button>
        </Link>
      )}
      {canPerformAction('delete_debt') && (
        <Button
          variant='outline'
          size='sm'
          onClick={() => handleDeleteDebt(debt)}
          className='text-red-600 hover:text-red-700'
        >
          Delete
        </Button>
      )}
    </div>
  )

  return (
    <div className='container mx-auto px-4 py-8'>
      <div className='flex justify-between items-center mb-8'>
        <div>
          <h1 className='text-3xl font-bold text-gray-900'>Debt Transactions</h1>
          <p className='text-gray-600 mt-2'>Track customer purchases on credit</p>
        </div>
        {canPerformAction('create_debt') && (
          <Link href='/debts/new'>
            <Button>Record New Debt</Button>
          </Link>
        )}
      </div>

      {/* Statistics Cards */}
      <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8'>
        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>Total Debts</CardTitle>
            <svg
              className='h-4 w-4 text-muted-foreground'
              fill='none'
              viewBox='0 0 24 24'
              stroke='currentColor'
            >
              <path
                strokeLinecap='round'
                strokeLinejoin='round'
                strokeWidth={2}
                d='M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z'
              />
            </svg>
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>{stats.total_debts}</div>
            <p className='text-xs text-muted-foreground'>All time transactions</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>Unpaid Debts</CardTitle>
            <svg
              className='h-4 w-4 text-muted-foreground'
              fill='none'
              viewBox='0 0 24 24'
              stroke='currentColor'
            >
              <path
                strokeLinecap='round'
                strokeLinejoin='round'
                strokeWidth={2}
                d='M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z'
              />
            </svg>
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold text-red-600'>{stats.unpaid_debts}</div>
            <p className='text-xs text-muted-foreground'>Outstanding transactions</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>Total Amount</CardTitle>
            <svg
              className='h-4 w-4 text-muted-foreground'
              fill='none'
              viewBox='0 0 24 24'
              stroke='currentColor'
            >
              <path
                strokeLinecap='round'
                strokeLinejoin='round'
                strokeWidth={2}
                d='M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1'
              />
            </svg>
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>
              {formatCurrency(stats.total_debt_amount, 'PHP')}
            </div>
            <p className='text-xs text-muted-foreground'>All debt transactions</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>Outstanding</CardTitle>
            <svg
              className='h-4 w-4 text-muted-foreground'
              fill='none'
              viewBox='0 0 24 24'
              stroke='currentColor'
            >
              <path
                strokeLinecap='round'
                strokeLinejoin='round'
                strokeWidth={2}
                d='M13 17h8m0 0V9m0 8l-8-8-4 4-6-6'
              />
            </svg>
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold text-red-600'>
              {formatCurrency(stats.unpaid_debt_amount, 'PHP')}
            </div>
            <p className='text-xs text-muted-foreground'>Unpaid amount</p>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card className='mb-6'>
        <CardHeader>
          <CardTitle>Filters</CardTitle>
          <CardDescription>Filter debt transactions by customer, status, and date</CardDescription>
        </CardHeader>
        <CardContent>
          <div className='grid grid-cols-1 md:grid-cols-4 gap-4'>
            <div>
              <label className='block text-sm font-medium text-gray-700 mb-1'>Customer</label>
              <select
                value={filters.customer_id || ''}
                onChange={(e) =>
                  setFilters((prev) => ({ ...prev, customer_id: e.target.value || undefined }))
                }
                className='w-full h-10 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'
              >
                <option value=''>All Customers</option>
                {customers.map((customer) => (
                  <option key={customer.id} value={customer.id}>
                    {customer.full_name}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className='block text-sm font-medium text-gray-700 mb-1'>Payment Status</label>
              <select
                value={filters.is_fully_paid?.toString() || ''}
                onChange={(e) =>
                  setFilters((prev) => ({
                    ...prev,
                    is_fully_paid: e.target.value ? e.target.value === 'true' : undefined,
                  }))
                }
                className='w-full h-10 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'
              >
                <option value=''>All Status</option>
                <option value='false'>Unpaid</option>
                <option value='true'>Paid</option>
              </select>
            </div>

            <div>
              <label className='block text-sm font-medium text-gray-700 mb-1'>Date From</label>
              <input
                type='date'
                value={filters.date_from || ''}
                onChange={(e) =>
                  setFilters((prev) => ({ ...prev, date_from: e.target.value || undefined }))
                }
                className='w-full h-10 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'
              />
            </div>

            <div>
              <label className='block text-sm font-medium text-gray-700 mb-1'>Date To</label>
              <input
                type='date'
                value={filters.date_to || ''}
                onChange={(e) =>
                  setFilters((prev) => ({ ...prev, date_to: e.target.value || undefined }))
                }
                className='w-full h-10 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'
              />
            </div>
          </div>

          <div className='mt-4'>
            <Button
              variant='outline'
              onClick={() => {
                setFilters({})
                setPagination((prev) => ({ ...prev, page: 1 }))
              }}
            >
              Clear Filters
            </Button>
          </div>
        </CardContent>
      </Card>

      {error && (
        <div className='bg-red-50 border border-red-200 rounded-md p-4 mb-6'>
          <p className='text-sm text-red-600'>{error}</p>
        </div>
      )}

      <DataTable
        data={debts}
        columns={columns}
        loading={loading}
        pagination={pagination}
        onPageChange={handlePageChange}
        onSearch={handleSearch}
        onSort={handleSort}
        searchPlaceholder='Search debts...'
        emptyMessage='No debt transactions found'
        actions={renderActions}
      />
    </div>
  )
}

export default function DebtsPage() {
  return (
    <ProtectedRoute requiredPermission='view_analytics'>
      <DebtsContent />
    </ProtectedRoute>
  )
}
