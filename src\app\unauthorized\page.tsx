'use client'

import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { But<PERSON> } from '@/components/ui/Button'
import { useAuth } from '@/components/providers/AuthProvider'

export default function UnauthorizedPage() {
  const { user, signOut } = useAuth()
  const router = useRouter()

  const handleSignOut = async () => {
    await signOut()
    router.push('/auth/login')
  }

  return (
    <div className='min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8'>
      <div className='max-w-md w-full text-center'>
        <div className='mb-8'>
          <svg
            className='mx-auto h-24 w-24 text-red-400'
            fill='none'
            viewBox='0 0 24 24'
            stroke='currentColor'
          >
            <path
              strokeLinecap='round'
              strokeLinejoin='round'
              strokeWidth={1}
              d='M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z'
            />
          </svg>
        </div>

        <h1 className='text-3xl font-bold text-gray-900 mb-4'>Access Denied</h1>

        <p className='text-gray-600 mb-2'>
          You don&apos;t have permission to access this resource.
        </p>

        {user?.profile && (
          <p className='text-sm text-gray-500 mb-8'>
            Current role: <span className='font-medium'>{user.profile.role}</span>
          </p>
        )}

        <div className='space-y-4'>
          <Link href='/dashboard'>
            <Button className='w-full'>Go to Dashboard</Button>
          </Link>

          <Button variant='outline' onClick={handleSignOut} className='w-full'>
            Sign Out
          </Button>
        </div>

        <div className='mt-8 text-sm text-gray-500'>
          <p>If you believe this is an error, please contact your administrator.</p>
        </div>
      </div>
    </div>
  )
}
