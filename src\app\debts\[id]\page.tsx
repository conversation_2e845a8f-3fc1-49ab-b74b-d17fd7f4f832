'use client'

import { useState, useEffect, useCallback } from 'react'
import { useParams } from 'next/navigation'
import Link from 'next/link'
import { ProtectedRoute } from '@/components/auth/ProtectedRoute'
import { usePermissions } from '@/components/providers/AuthProvider'
import { Button } from '@/components/ui/Button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card'
import { debtService } from '@/lib/services/debtService'
import { formatCurrency, formatDate } from '@/utils'
import type { DebtTransaction } from '@/types'

function DebtDetailContent() {
  const params = useParams()

  const { canPerformAction } = usePermissions()
  const [debt, setDebt] = useState<DebtTransaction | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')

  const debtId = params.id as string

  const loadDebtData = useCallback(async () => {
    try {
      setLoading(true)
      const data = await debtService.getDebt(debtId)
      setDebt(data)
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to load debt data')
      console.error('Failed to load debt data:', error)
    } finally {
      setLoading(false)
    }
  }, [debtId])

  useEffect(() => {
    if (debtId) {
      loadDebtData()
    }
  }, [debtId, loadDebtData])

  const handleMarkAsPaid = async () => {
    if (!debt || !canPerformAction('edit_debt')) {
      alert('You do not have permission to mark debts as paid')
      return
    }

    if (
      !confirm(
        'Are you sure you want to mark this debt as fully paid? This action cannot be undone.'
      )
    ) {
      return
    }

    try {
      await debtService.markDebtAsPaid(debt.id)
      loadDebtData() // Reload the data
    } catch (error) {
      alert(error instanceof Error ? error.message : 'Failed to mark debt as paid')
    }
  }

  if (loading) {
    return (
      <div className='container mx-auto px-4 py-8'>
        <div className='flex items-center justify-center min-h-64'>
          <div className='animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600' />
          <span className='ml-2 text-gray-500'>Loading debt data...</span>
        </div>
      </div>
    )
  }

  if (error || !debt) {
    return (
      <div className='container mx-auto px-4 py-8'>
        <div className='text-center'>
          <h1 className='text-2xl font-bold text-gray-900 mb-4'>Debt Transaction Not Found</h1>
          <p className='text-gray-600 mb-6'>
            {error || 'The requested debt transaction could not be found.'}
          </p>
          <Link href='/debts'>
            <Button>Back to Debts</Button>
          </Link>
        </div>
      </div>
    )
  }

  const paymentProgress = ((debt.total_amount - debt.remaining_balance) / debt.total_amount) * 100

  return (
    <div className='container mx-auto px-4 py-8'>
      <div className='flex justify-between items-center mb-8'>
        <div>
          <h1 className='text-3xl font-bold text-gray-900'>Debt Transaction Details</h1>
          <p className='text-gray-600 mt-2'>Transaction ID: {debt.id}</p>
        </div>
        <div className='flex space-x-4'>
          {canPerformAction('process_payment') && !debt.is_fully_paid && (
            <Link href={`/payments/new?debt_id=${debt.id}`}>
              <Button>Process Payment</Button>
            </Link>
          )}
          {canPerformAction('edit_debt') && (
            <Link href={`/debts/${debt.id}/edit`}>
              <Button variant='outline'>Edit</Button>
            </Link>
          )}
          <Link href='/debts'>
            <Button variant='outline'>Back to Debts</Button>
          </Link>
        </div>
      </div>

      <div className='grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8'>
        {/* Customer Information */}
        <Card>
          <CardHeader>
            <CardTitle>Customer Information</CardTitle>
          </CardHeader>
          <CardContent className='space-y-4'>
            <div>
              <label className='text-sm font-medium text-gray-500'>Customer Name</label>
              <p className='text-gray-900 font-medium'>{debt.customer?.full_name}</p>
            </div>
            {debt.customer?.phone && (
              <div>
                <label className='text-sm font-medium text-gray-500'>Phone</label>
                <p className='text-gray-900'>{debt.customer.phone}</p>
              </div>
            )}
            {debt.customer?.email && (
              <div>
                <label className='text-sm font-medium text-gray-500'>Email</label>
                <p className='text-gray-900'>{debt.customer.email}</p>
              </div>
            )}
            <div>
              <label className='text-sm font-medium text-gray-500'>Total Customer Debt</label>
              <p
                className={`font-bold text-lg ${debt.customer?.total_debt && debt.customer.total_debt > 0 ? 'text-red-600' : 'text-green-600'}`}
              >
                {formatCurrency(debt.customer?.total_debt || 0, 'PHP')}
              </p>
            </div>
            <div className='pt-2'>
              <Link href={`/customers/${debt.customer?.id}`}>
                <Button variant='outline' size='sm'>
                  View Customer Profile
                </Button>
              </Link>
            </div>
          </CardContent>
        </Card>

        {/* Product Information */}
        <Card>
          <CardHeader>
            <CardTitle>Product Information</CardTitle>
          </CardHeader>
          <CardContent className='space-y-4'>
            <div>
              <label className='text-sm font-medium text-gray-500'>Product Name</label>
              <p className='text-gray-900 font-medium'>{debt.product_name}</p>
            </div>
            <div>
              <label className='text-sm font-medium text-gray-500'>Unit Price</label>
              <p className='text-gray-900'>{formatCurrency(debt.product_price, 'PHP')}</p>
            </div>
            <div>
              <label className='text-sm font-medium text-gray-500'>Quantity</label>
              <p className='text-gray-900'>{debt.quantity}</p>
            </div>
            <div>
              <label className='text-sm font-medium text-gray-500'>Total Amount</label>
              <p className='text-gray-900 font-bold text-lg'>
                {formatCurrency(debt.total_amount, 'PHP')}
              </p>
            </div>
            {debt.product && (
              <div className='pt-2'>
                <Link href={`/products/${debt.product.id}`}>
                  <Button variant='outline' size='sm'>
                    View Product Details
                  </Button>
                </Link>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Payment Status */}
      <Card className='mb-8'>
        <CardHeader>
          <CardTitle>Payment Status</CardTitle>
          <CardDescription>Current payment progress for this debt transaction</CardDescription>
        </CardHeader>
        <CardContent>
          <div className='space-y-6'>
            <div className='grid grid-cols-1 md:grid-cols-3 gap-6'>
              <div className='text-center'>
                <div className='text-2xl font-bold text-gray-900'>
                  {formatCurrency(debt.total_amount, 'PHP')}
                </div>
                <div className='text-sm text-gray-500'>Total Amount</div>
              </div>
              <div className='text-center'>
                <div className='text-2xl font-bold text-green-600'>
                  {formatCurrency(debt.total_amount - debt.remaining_balance, 'PHP')}
                </div>
                <div className='text-sm text-gray-500'>Amount Paid</div>
              </div>
              <div className='text-center'>
                <div
                  className={`text-2xl font-bold ${debt.remaining_balance > 0 ? 'text-red-600' : 'text-green-600'}`}
                >
                  {formatCurrency(debt.remaining_balance, 'PHP')}
                </div>
                <div className='text-sm text-gray-500'>Remaining Balance</div>
              </div>
            </div>

            {/* Progress Bar */}
            <div>
              <div className='flex justify-between text-sm text-gray-600 mb-2'>
                <span>Payment Progress</span>
                <span>{paymentProgress.toFixed(1)}%</span>
              </div>
              <div className='w-full bg-gray-200 rounded-full h-3'>
                <div
                  className={`h-3 rounded-full transition-all duration-300 ${
                    debt.is_fully_paid ? 'bg-green-500' : 'bg-blue-500'
                  }`}
                  style={{ width: `${paymentProgress}%` }}
                />
              </div>
            </div>

            {/* Status Badge */}
            <div className='flex justify-center'>
              <span
                className={`inline-flex items-center px-4 py-2 rounded-full text-sm font-medium ${
                  debt.is_fully_paid ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                }`}
              >
                {debt.is_fully_paid ? '✓ Fully Paid' : '⏳ Outstanding'}
              </span>
            </div>

            {/* Admin Actions */}
            {canPerformAction('edit_debt') && !debt.is_fully_paid && (
              <div className='flex justify-center pt-4'>
                <Button
                  variant='outline'
                  onClick={handleMarkAsPaid}
                  className='text-green-600 hover:text-green-700'
                >
                  Mark as Fully Paid
                </Button>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Transaction Details */}
      <Card>
        <CardHeader>
          <CardTitle>Transaction Details</CardTitle>
        </CardHeader>
        <CardContent className='space-y-4'>
          <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
            <div>
              <label className='text-sm font-medium text-gray-500'>Transaction Date</label>
              <p className='text-gray-900'>{formatDate(debt.debt_date)}</p>
            </div>
            <div>
              <label className='text-sm font-medium text-gray-500'>Last Updated</label>
              <p className='text-gray-900'>{formatDate(debt.updated_at)}</p>
            </div>
          </div>

          {debt.notes && (
            <div>
              <label className='text-sm font-medium text-gray-500'>Notes</label>
              <p className='text-gray-900 bg-gray-50 p-3 rounded-md'>{debt.notes}</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}

export default function DebtDetailPage() {
  return (
    <ProtectedRoute requiredPermission='view_analytics'>
      <DebtDetailContent />
    </ProtectedRoute>
  )
}
