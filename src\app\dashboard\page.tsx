'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { ProtectedRoute } from '@/components/auth/ProtectedRoute'
import { useAuth, usePermissions } from '@/components/providers/AuthProvider'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import {
  MetricCard,
  SimpleBarChart,
  SimplePieChart,
  SimpleLineChart,
} from '@/components/charts/SimpleChart'
import { analyticsService } from '@/lib/services/analyticsService'
import { formatCurrency, formatDate } from '@/utils'
import type {
  DashboardStats,
  SalesAnalytics,
  CustomerAnalytics,
} from '@/lib/services/analyticsService'

function DashboardContent() {
  const { user } = useAuth()
  const { canPerformAction } = usePermissions()
  const [stats, setStats] = useState<DashboardStats | null>(null)
  const [salesAnalytics, setSalesAnalytics] = useState<SalesAnalytics | null>(null)
  const [customerAnalytics, setCustomerAnalytics] = useState<CustomerAnalytics | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')

  useEffect(() => {
    loadDashboardData()
  }, [])

  const loadDashboardData = async () => {
    try {
      setLoading(true)
      const [dashboardStats, salesData, customerData] = await Promise.all([
        analyticsService.getDashboardStats(),
        analyticsService.getSalesAnalytics(30),
        analyticsService.getCustomerAnalytics(),
      ])

      setStats(dashboardStats)
      setSalesAnalytics(salesData)
      setCustomerAnalytics(customerData)
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to load dashboard data')
      console.error('Dashboard data loading error:', error)
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <div className='min-h-screen bg-gray-50'>
        <div className='container mx-auto px-4 py-12'>
          <div className='flex flex-col items-center justify-center min-h-96 space-y-4'>
            <div className='loading-spinner w-8 h-8' />
            <div className='text-center'>
              <h3 className='text-lg font-medium text-gray-900 mb-2'>Loading Dashboard</h3>
              <p className='text-gray-500'>Please wait while we fetch your data...</p>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className='min-h-screen bg-gray-50'>
      <div className='container mx-auto px-4 py-8'>
        {/* Welcome Header */}
        <div className='mb-8'>
          <div className='flex items-center justify-between'>
            <div>
              <h1 className='text-3xl font-bold text-gray-900 mb-2'>
                Welcome back, {user?.profile?.full_name || user?.email}! 👋
              </h1>
              <div className='flex items-center space-x-4 text-sm'>
                <div className='flex items-center space-x-2'>
                  <span className='w-2 h-2 bg-green-500 rounded-full'></span>
                  <span className='text-gray-600'>
                    Role:{' '}
                    <span className='font-medium capitalize text-gray-900'>
                      {user?.profile?.role}
                    </span>
                  </span>
                </div>
                <span className='text-gray-400'>•</span>
                <span className='text-gray-500'>
                  Last updated: {formatDate(new Date().toISOString())}
                </span>
              </div>
            </div>
            <Button
              variant='outline'
              size='sm'
              onClick={loadDashboardData}
              className='hidden sm:flex'
            >
              <svg className='w-4 h-4 mr-2' fill='none' stroke='currentColor' viewBox='0 0 24 24'>
                <path
                  strokeLinecap='round'
                  strokeLinejoin='round'
                  strokeWidth={2}
                  d='M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15'
                />
              </svg>
              Refresh
            </Button>
          </div>
        </div>

        {error && (
          <div className='bg-red-50 border border-red-200 rounded-xl p-4 mb-6 flex items-start space-x-3'>
            <svg
              className='w-5 h-5 text-red-500 mt-0.5'
              fill='none'
              stroke='currentColor'
              viewBox='0 0 24 24'
            >
              <path
                strokeLinecap='round'
                strokeLinejoin='round'
                strokeWidth={2}
                d='M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z'
              />
            </svg>
            <div>
              <h4 className='text-sm font-medium text-red-800'>Error Loading Dashboard</h4>
              <p className='text-sm text-red-600 mt-1'>{error}</p>
            </div>
          </div>
        )}

        {/* Key Metrics */}
        {stats && (
          <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8'>
            <div className='metric-card group'>
              <div className='flex items-center justify-between mb-4'>
                <div className='metric-card-icon blue'>
                  <svg className='h-6 w-6' fill='none' viewBox='0 0 24 24' stroke='currentColor'>
                    <path
                      strokeLinecap='round'
                      strokeLinejoin='round'
                      strokeWidth={2}
                      d='M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z'
                    />
                  </svg>
                </div>
                <div className='text-right'>
                  <p className='text-2xl font-bold text-gray-900'>{stats.active_customers}</p>
                  <p className='text-sm font-medium text-gray-600'>Total Customers</p>
                </div>
              </div>
              <div className='flex items-center text-sm text-green-600'>
                <svg className='w-4 h-4 mr-1' fill='none' stroke='currentColor' viewBox='0 0 24 24'>
                  <path
                    strokeLinecap='round'
                    strokeLinejoin='round'
                    strokeWidth={2}
                    d='M13 7h8m0 0v8m0-8l-8 8-4-4-6 6'
                  />
                </svg>
                <span>Active accounts</span>
              </div>
            </div>

            <div className='metric-card group'>
              <div className='flex items-center justify-between mb-4'>
                <div className='metric-card-icon green'>
                  <svg className='h-6 w-6' fill='none' viewBox='0 0 24 24' stroke='currentColor'>
                    <path
                      strokeLinecap='round'
                      strokeLinejoin='round'
                      strokeWidth={2}
                      d='M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4'
                    />
                  </svg>
                </div>
                <div className='text-right'>
                  <p className='text-2xl font-bold text-gray-900'>{stats.active_products}</p>
                  <p className='text-sm font-medium text-gray-600'>Active Products</p>
                </div>
              </div>
              <div className='flex items-center text-sm text-blue-600'>
                <svg className='w-4 h-4 mr-1' fill='none' stroke='currentColor' viewBox='0 0 24 24'>
                  <path
                    strokeLinecap='round'
                    strokeLinejoin='round'
                    strokeWidth={2}
                    d='M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4'
                  />
                </svg>
                <span>In inventory</span>
              </div>
            </div>

            <div className='metric-card group'>
              <div className='flex items-center justify-between mb-4'>
                <div className='metric-card-icon red'>
                  <svg className='h-6 w-6' fill='none' viewBox='0 0 24 24' stroke='currentColor'>
                    <path
                      strokeLinecap='round'
                      strokeLinejoin='round'
                      strokeWidth={2}
                      d='M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1'
                    />
                  </svg>
                </div>
                <div className='text-right'>
                  <p className='text-2xl font-bold text-gray-900'>
                    {formatCurrency(stats.outstanding_debt, 'PHP')}
                  </p>
                  <p className='text-sm font-medium text-gray-600'>Outstanding Debt</p>
                </div>
              </div>
              <div className='flex items-center text-sm text-red-600'>
                <svg className='w-4 h-4 mr-1' fill='none' stroke='currentColor' viewBox='0 0 24 24'>
                  <path
                    strokeLinecap='round'
                    strokeLinejoin='round'
                    strokeWidth={2}
                    d='M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z'
                  />
                </svg>
                <span>Needs attention</span>
              </div>
            </div>

            <div className='metric-card group'>
              <div className='flex items-center justify-between mb-4'>
                <div className='metric-card-icon green'>
                  <svg className='h-6 w-6' fill='none' viewBox='0 0 24 24' stroke='currentColor'>
                    <path
                      strokeLinecap='round'
                      strokeLinejoin='round'
                      strokeWidth={2}
                      d='M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z'
                    />
                  </svg>
                </div>
                <div className='text-right'>
                  <p className='text-2xl font-bold text-gray-900'>
                    {formatCurrency(stats.today_payments, 'PHP')}
                  </p>
                  <p className='text-sm font-medium text-gray-600'>Today&apos;s Payments</p>
                </div>
              </div>
              <div className='flex items-center text-sm text-green-600'>
                <svg className='w-4 h-4 mr-1' fill='none' stroke='currentColor' viewBox='0 0 24 24'>
                  <path
                    strokeLinecap='round'
                    strokeLinejoin='round'
                    strokeWidth={2}
                    d='M13 7h8m0 0v8m0-8l-8 8-4-4-6 6'
                  />
                </svg>
                <span>Received today</span>
              </div>
            </div>
          </div>
        )}

        {/* Additional Metrics */}
        {stats && (
          <div className='grid grid-cols-1 md:grid-cols-3 gap-6 mb-8'>
            <div className='metric-card group'>
              <div className='flex items-center justify-between mb-4'>
                <div className='metric-card-icon purple'>
                  <svg className='h-6 w-6' fill='none' viewBox='0 0 24 24' stroke='currentColor'>
                    <path
                      strokeLinecap='round'
                      strokeLinejoin='round'
                      strokeWidth={2}
                      d='M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z'
                    />
                  </svg>
                </div>
                <div className='text-right'>
                  <p className='text-2xl font-bold text-gray-900'>
                    {stats.payment_rate.toFixed(1)}%
                  </p>
                  <p className='text-sm font-medium text-gray-600'>Payment Rate</p>
                </div>
              </div>
              <div className='flex items-center text-sm text-purple-600'>
                <svg className='w-4 h-4 mr-1' fill='none' stroke='currentColor' viewBox='0 0 24 24'>
                  <path
                    strokeLinecap='round'
                    strokeLinejoin='round'
                    strokeWidth={2}
                    d='M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z'
                  />
                </svg>
                <span>Collection efficiency</span>
              </div>
            </div>

            <div className='metric-card group'>
              <div className='flex items-center justify-between mb-4'>
                <div className='metric-card-icon yellow'>
                  <svg className='h-6 w-6' fill='none' viewBox='0 0 24 24' stroke='currentColor'>
                    <path
                      strokeLinecap='round'
                      strokeLinejoin='round'
                      strokeWidth={2}
                      d='M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z'
                    />
                  </svg>
                </div>
                <div className='text-right'>
                  <p className='text-2xl font-bold text-gray-900'>{stats.low_stock_products}</p>
                  <p className='text-sm font-medium text-gray-600'>Low Stock Items</p>
                </div>
              </div>
              <div className='flex items-center text-sm text-yellow-600'>
                <svg className='w-4 h-4 mr-1' fill='none' stroke='currentColor' viewBox='0 0 24 24'>
                  <path
                    strokeLinecap='round'
                    strokeLinejoin='round'
                    strokeWidth={2}
                    d='M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z'
                  />
                </svg>
                <span>Restock needed</span>
              </div>
            </div>

            <div className='metric-card group'>
              <div className='flex items-center justify-between mb-4'>
                <div className='metric-card-icon green'>
                  <svg className='h-6 w-6' fill='none' viewBox='0 0 24 24' stroke='currentColor'>
                    <path
                      strokeLinecap='round'
                      strokeLinejoin='round'
                      strokeWidth={2}
                      d='M13 7h8m0 0v8m0-8l-8 8-4-4-6 6'
                    />
                  </svg>
                </div>
                <div className='text-right'>
                  <p className='text-2xl font-bold text-gray-900'>
                    {formatCurrency(stats.this_month_revenue, 'PHP')}
                  </p>
                  <p className='text-sm font-medium text-gray-600'>This Month Revenue</p>
                </div>
              </div>
              <div className='flex items-center text-sm text-green-600'>
                <svg className='w-4 h-4 mr-1' fill='none' stroke='currentColor' viewBox='0 0 24 24'>
                  <path
                    strokeLinecap='round'
                    strokeLinejoin='round'
                    strokeWidth={2}
                    d='M13 7h8m0 0v8m0-8l-8 8-4-4-6 6'
                  />
                </svg>
                <span>Monthly earnings</span>
              </div>
            </div>
          </div>
        )}

        {/* Charts and Analytics */}
        <div className='grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8'>
          {/* Sales Trend */}
          {salesAnalytics && (
            <Card variant='elevated' className='p-6'>
              <div className='flex items-center justify-between mb-6'>
                <div>
                  <h3 className='text-lg font-semibold text-gray-900'>Sales Trend</h3>
                  <p className='text-sm text-gray-600'>Last 7 days performance</p>
                </div>
                <div className='w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center'>
                  <svg
                    className='w-5 h-5 text-green-600'
                    fill='none'
                    stroke='currentColor'
                    viewBox='0 0 24 24'
                  >
                    <path
                      strokeLinecap='round'
                      strokeLinejoin='round'
                      strokeWidth={2}
                      d='M13 7h8m0 0v8m0-8l-8 8-4-4-6 6'
                    />
                  </svg>
                </div>
              </div>
              <SimpleLineChart
                data={salesAnalytics.daily_sales.slice(-7).map((day) => ({
                  label: new Date(day.date).toLocaleDateString('en-US', {
                    month: 'short',
                    day: 'numeric',
                  }),
                  value: day.total_amount,
                }))}
                title=''
                formatValue={(value) => formatCurrency(value, 'PHP')}
                color='#10B981'
              />
            </Card>
          )}

          {/* Top Products */}
          {salesAnalytics && (
            <Card variant='elevated' className='p-6'>
              <div className='flex items-center justify-between mb-6'>
                <div>
                  <h3 className='text-lg font-semibold text-gray-900'>Top Products</h3>
                  <p className='text-sm text-gray-600'>Best sellers this month</p>
                </div>
                <div className='w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center'>
                  <svg
                    className='w-5 h-5 text-blue-600'
                    fill='none'
                    stroke='currentColor'
                    viewBox='0 0 24 24'
                  >
                    <path
                      strokeLinecap='round'
                      strokeLinejoin='round'
                      strokeWidth={2}
                      d='M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4'
                    />
                  </svg>
                </div>
              </div>
              <SimpleBarChart
                data={salesAnalytics.top_products.slice(0, 5).map((product) => ({
                  label:
                    product.product_name.length > 15
                      ? `${product.product_name.substring(0, 15)}...`
                      : product.product_name,
                  value: product.total_amount,
                }))}
                title=''
                formatValue={(value) => formatCurrency(value, 'PHP')}
              />
            </Card>
          )}
        </div>

        <div className='grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8'>
          {/* Payment Methods */}
          {salesAnalytics && (
            <Card variant='elevated' className='p-6'>
              <div className='flex items-center justify-between mb-6'>
                <div>
                  <h3 className='text-lg font-semibold text-gray-900'>Payment Methods</h3>
                  <p className='text-sm text-gray-600'>Distribution by payment type</p>
                </div>
                <div className='w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center'>
                  <svg
                    className='w-5 h-5 text-purple-600'
                    fill='none'
                    stroke='currentColor'
                    viewBox='0 0 24 24'
                  >
                    <path
                      strokeLinecap='round'
                      strokeLinejoin='round'
                      strokeWidth={2}
                      d='M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z'
                    />
                  </svg>
                </div>
              </div>
              <SimplePieChart
                data={salesAnalytics.payment_methods.map((method) => ({
                  label: method.method.replace('_', ' ').toUpperCase(),
                  value: method.total_amount,
                }))}
                title=''
                formatValue={(value) => formatCurrency(value, 'PHP')}
              />
            </Card>
          )}

          {/* Customer Segments */}
          {customerAnalytics && (
            <Card variant='elevated' className='p-6'>
              <div className='flex items-center justify-between mb-6'>
                <div>
                  <h3 className='text-lg font-semibold text-gray-900'>Customer Segments</h3>
                  <p className='text-sm text-gray-600'>Debt distribution analysis</p>
                </div>
                <div className='w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center'>
                  <svg
                    className='w-5 h-5 text-orange-600'
                    fill='none'
                    stroke='currentColor'
                    viewBox='0 0 24 24'
                  >
                    <path
                      strokeLinecap='round'
                      strokeLinejoin='round'
                      strokeWidth={2}
                      d='M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z'
                    />
                  </svg>
                </div>
              </div>
              <SimplePieChart
                data={[
                  { label: 'No Debt', value: customerAnalytics.customer_segments.no_debt },
                  { label: 'Low Debt', value: customerAnalytics.customer_segments.low_debt },
                  { label: 'Medium Debt', value: customerAnalytics.customer_segments.medium_debt },
                  { label: 'High Debt', value: customerAnalytics.customer_segments.high_debt },
                ]}
                title=''
                formatValue={(value) => `${value} customers`}
              />
            </Card>
          )}
        </div>

        {/* Quick Actions */}
        <div className='mb-8'>
          <div className='flex items-center justify-between mb-6'>
            <div>
              <h2 className='text-xl font-semibold text-gray-900'>Quick Actions</h2>
              <p className='text-sm text-gray-600'>Manage your store operations</p>
            </div>
          </div>

          <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'>
            {canPerformAction('create_product') && (
              <Card
                variant='elevated'
                className='group hover:shadow-xl transition-all duration-300'
              >
                <CardHeader>
                  <div className='flex items-center space-x-3'>
                    <div className='w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center group-hover:bg-blue-200 transition-colors'>
                      <svg
                        className='w-5 h-5 text-blue-600'
                        fill='none'
                        stroke='currentColor'
                        viewBox='0 0 24 24'
                      >
                        <path
                          strokeLinecap='round'
                          strokeLinejoin='round'
                          strokeWidth={2}
                          d='M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4'
                        />
                      </svg>
                    </div>
                    <div>
                      <CardTitle className='text-base'>Product Management</CardTitle>
                      <CardDescription>Add, edit, and manage your store inventory</CardDescription>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className='space-y-3'>
                    <Link href='/products' className='block'>
                      <Button className='w-full' variant='outline' size='sm'>
                        📦 View Products
                      </Button>
                    </Link>
                    <Link href='/products/new' className='block'>
                      <Button className='w-full' size='sm'>
                        ➕ Add New Product
                      </Button>
                    </Link>
                  </div>
                </CardContent>
              </Card>
            )}

            {canPerformAction('create_customer') && (
              <Card
                variant='elevated'
                className='group hover:shadow-xl transition-all duration-300'
              >
                <CardHeader>
                  <div className='flex items-center space-x-3'>
                    <div className='w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center group-hover:bg-green-200 transition-colors'>
                      <svg
                        className='w-5 h-5 text-green-600'
                        fill='none'
                        stroke='currentColor'
                        viewBox='0 0 24 24'
                      >
                        <path
                          strokeLinecap='round'
                          strokeLinejoin='round'
                          strokeWidth={2}
                          d='M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z'
                        />
                      </svg>
                    </div>
                    <div>
                      <CardTitle className='text-base'>Customer Management</CardTitle>
                      <CardDescription>Manage customer information and profiles</CardDescription>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className='space-y-3'>
                    <Link href='/customers' className='block'>
                      <Button className='w-full' variant='outline' size='sm'>
                        👥 View Customers
                      </Button>
                    </Link>
                    <Link href='/customers/new' className='block'>
                      <Button className='w-full' size='sm'>
                        👤 Add New Customer
                      </Button>
                    </Link>
                  </div>
                </CardContent>
              </Card>
            )}

            {canPerformAction('create_debt') && (
              <Card
                variant='elevated'
                className='group hover:shadow-xl transition-all duration-300'
              >
                <CardHeader>
                  <div className='flex items-center space-x-3'>
                    <div className='w-10 h-10 bg-red-100 rounded-lg flex items-center justify-center group-hover:bg-red-200 transition-colors'>
                      <svg
                        className='w-5 h-5 text-red-600'
                        fill='none'
                        stroke='currentColor'
                        viewBox='0 0 24 24'
                      >
                        <path
                          strokeLinecap='round'
                          strokeLinejoin='round'
                          strokeWidth={2}
                          d='M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1'
                        />
                      </svg>
                    </div>
                    <div>
                      <CardTitle className='text-base'>Debt Management</CardTitle>
                      <CardDescription>Track customer debts and payment history</CardDescription>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className='space-y-3'>
                    <Link href='/debts' className='block'>
                      <Button className='w-full' variant='outline' size='sm'>
                        💳 View Debts
                      </Button>
                    </Link>
                    <Link href='/debts/new' className='block'>
                      <Button className='w-full' size='sm'>
                        📝 Record New Debt
                      </Button>
                    </Link>
                  </div>
                </CardContent>
              </Card>
            )}

            {canPerformAction('process_payment') && (
              <Card
                variant='elevated'
                className='group hover:shadow-xl transition-all duration-300'
              >
                <CardHeader>
                  <div className='flex items-center space-x-3'>
                    <div className='w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center group-hover:bg-purple-200 transition-colors'>
                      <svg
                        className='w-5 h-5 text-purple-600'
                        fill='none'
                        stroke='currentColor'
                        viewBox='0 0 24 24'
                      >
                        <path
                          strokeLinecap='round'
                          strokeLinejoin='round'
                          strokeWidth={2}
                          d='M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z'
                        />
                      </svg>
                    </div>
                    <div>
                      <CardTitle className='text-base'>Payment Processing</CardTitle>
                      <CardDescription>
                        Process customer payments and update balances
                      </CardDescription>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className='space-y-3'>
                    <Link href='/payments' className='block'>
                      <Button className='w-full' variant='outline' size='sm'>
                        💰 View Payments
                      </Button>
                    </Link>
                    <Link href='/payments/new' className='block'>
                      <Button className='w-full' size='sm'>
                        💸 Process Payment
                      </Button>
                    </Link>
                  </div>
                </CardContent>
              </Card>
            )}

            {canPerformAction('view_analytics') && (
              <Card
                variant='elevated'
                className='group hover:shadow-xl transition-all duration-300'
              >
                <CardHeader>
                  <div className='flex items-center space-x-3'>
                    <div className='w-10 h-10 bg-indigo-100 rounded-lg flex items-center justify-center group-hover:bg-indigo-200 transition-colors'>
                      <svg
                        className='w-5 h-5 text-indigo-600'
                        fill='none'
                        stroke='currentColor'
                        viewBox='0 0 24 24'
                      >
                        <path
                          strokeLinecap='round'
                          strokeLinejoin='round'
                          strokeWidth={2}
                          d='M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z'
                        />
                      </svg>
                    </div>
                    <div>
                      <CardTitle className='text-base'>Reports & Analytics</CardTitle>
                      <CardDescription>View sales reports and business analytics</CardDescription>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className='space-y-3'>
                    <Link href='/reports' className='block'>
                      <Button className='w-full' variant='outline' size='sm'>
                        📊 View Reports
                      </Button>
                    </Link>
                    <Button
                      className='w-full'
                      variant='outline'
                      size='sm'
                      onClick={loadDashboardData}
                    >
                      🔄 Refresh Data
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}

            {canPerformAction('manage_users') && (
              <Card
                variant='elevated'
                className='group hover:shadow-xl transition-all duration-300'
              >
                <CardHeader>
                  <div className='flex items-center space-x-3'>
                    <div className='w-10 h-10 bg-yellow-100 rounded-lg flex items-center justify-center group-hover:bg-yellow-200 transition-colors'>
                      <svg
                        className='w-5 h-5 text-yellow-600'
                        fill='none'
                        stroke='currentColor'
                        viewBox='0 0 24 24'
                      >
                        <path
                          strokeLinecap='round'
                          strokeLinejoin='round'
                          strokeWidth={2}
                          d='M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z'
                        />
                      </svg>
                    </div>
                    <div>
                      <CardTitle className='text-base'>User Management</CardTitle>
                      <CardDescription>Manage admin users and permissions</CardDescription>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className='space-y-3'>
                    <Button className='w-full' variant='outline' size='sm'>
                      👨‍💼 View Users
                    </Button>
                    <Button className='w-full' size='sm'>
                      ➕ Add New User
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}

export default function DashboardPage() {
  return (
    <ProtectedRoute requiredRole={['super_admin', 'admin', 'cashier']}>
      <DashboardContent />
    </ProtectedRoute>
  )
}
