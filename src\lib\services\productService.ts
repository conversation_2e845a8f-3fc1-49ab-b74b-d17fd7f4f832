import { supabase, queries } from '@/lib/supabase'
import type {
  Product,
  Category,
  ProductFormData,
  ProductFilters,
  PaginationParams,
  PaginatedResponse,
} from '@/types'

export const productService = {
  // Get all products with pagination and filters
  getProducts: async (
    params: PaginationParams & ProductFilters = { page: 1, limit: 10 }
  ): Promise<PaginatedResponse<Product>> => {
    const {
      page,
      limit,
      search,
      category_id,
      is_active,
      low_stock,
      price_min,
      price_max,
      sort_by = 'name',
      sort_order = 'asc',
    } = params

    let query = supabase.from('products').select(
      `
        *,
        category:categories(*)
      `,
      { count: 'exact' }
    )

    // Apply filters
    if (search) {
      query = query.or(
        `name.ilike.%${search}%,description.ilike.%${search}%,barcode.ilike.%${search}%`
      )
    }

    if (category_id) {
      query = query.eq('category_id', category_id)
    }

    if (typeof is_active === 'boolean') {
      query = query.eq('is_active', is_active)
    }

    if (low_stock) {
      query = query.lte('stock_quantity', 10)
    }

    if (price_min !== undefined) {
      query = query.gte('price', price_min)
    }

    if (price_max !== undefined) {
      query = query.lte('price', price_max)
    }

    // Apply sorting
    query = query.order(sort_by, { ascending: sort_order === 'asc' })

    // Apply pagination
    const from = (page - 1) * limit
    const to = from + limit - 1
    query = query.range(from, to)

    const { data, error, count } = await query

    if (error) {
      throw new Error(error.message)
    }

    return {
      data: data || [],
      pagination: {
        page,
        limit,
        total: count || 0,
        totalPages: Math.ceil((count || 0) / limit),
      },
    }
  },

  // Get single product by ID
  getProduct: async (id: string): Promise<Product | null> => {
    const { data, error } = await supabase
      .from('products')
      .select(
        `
        *,
        category:categories(*)
      `
      )
      .eq('id', id)
      .single()

    if (error) {
      if (error.code === 'PGRST116') return null // Not found
      throw new Error(error.message)
    }

    return data
  },

  // Create new product
  createProduct: async (productData: ProductFormData): Promise<Product> => {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { image, ...data } = productData

    const { data: product, error } = await supabase
      .from('products')
      .insert([data])
      .select(
        `
        *,
        category:categories(*)
      `
      )
      .single()

    if (error) {
      throw new Error(error.message)
    }

    return product
  },

  // Update product
  updateProduct: async (id: string, productData: Partial<ProductFormData>): Promise<Product> => {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { image, ...data } = productData

    const { data: product, error } = await supabase
      .from('products')
      .update(data)
      .eq('id', id)
      .select(
        `
        *,
        category:categories(*)
      `
      )
      .single()

    if (error) {
      throw new Error(error.message)
    }

    return product
  },

  // Delete product
  deleteProduct: async (id: string): Promise<void> => {
    const { error } = await supabase.from('products').delete().eq('id', id)

    if (error) {
      throw new Error(error.message)
    }
  },

  // Update product stock
  updateStock: async (
    id: string,
    quantity: number,
    reason: string = 'manual_adjustment'
  ): Promise<Product> => {
    // Get current product
    const product = await productService.getProduct(id)
    if (!product) {
      throw new Error('Product not found')
    }

    const previousStock = product.stock_quantity
    const newStock = quantity

    // Update product stock
    const { data: updatedProduct, error: updateError } = await supabase
      .from('products')
      .update({ stock_quantity: newStock })
      .eq('id', id)
      .select(
        `
        *,
        category:categories(*)
      `
      )
      .single()

    if (updateError) {
      throw new Error(updateError.message)
    }

    // Record inventory movement
    const { error: movementError } = await supabase.from('inventory_movements').insert([
      {
        product_id: id,
        movement_type:
          newStock > previousStock ? 'in' : newStock < previousStock ? 'out' : 'adjustment',
        quantity: Math.abs(newStock - previousStock),
        previous_stock: previousStock,
        new_stock: newStock,
        reason,
      },
    ])

    if (movementError) {
      console.error('Failed to record inventory movement:', movementError)
    }

    return updatedProduct
  },

  // Get low stock products
  getLowStockProducts: async (threshold: number = 10): Promise<Product[]> => {
    const { data, error } = await queries.getLowStockProducts(threshold)

    if (error) {
      throw new Error(error.message)
    }

    return data || []
  },

  // Bulk update products
  bulkUpdateProducts: async (
    updates: Array<{ id: string; data: Partial<ProductFormData> }>
  ): Promise<Product[]> => {
    const results = await Promise.all(
      updates.map(({ id, data }) => productService.updateProduct(id, data))
    )
    return results
  },

  // Get product inventory movements
  getInventoryMovements: async (productId: string): Promise<Record<string, unknown>[]> => {
    const { data, error } = await supabase
      .from('inventory_movements')
      .select('*')
      .eq('product_id', productId)
      .order('created_at', { ascending: false })

    if (error) {
      throw new Error(error.message)
    }

    return data || []
  },
}

// Category service
export const categoryService = {
  // Get all categories
  getCategories: async (): Promise<Category[]> => {
    const { data, error } = await supabase.from('categories').select('*').order('name')

    if (error) {
      throw new Error(error.message)
    }

    return data || []
  },

  // Create category
  createCategory: async (categoryData: {
    name: string
    description?: string
  }): Promise<Category> => {
    const { data, error } = await supabase
      .from('categories')
      .insert([categoryData])
      .select()
      .single()

    if (error) {
      throw new Error(error.message)
    }

    return data
  },

  // Update category
  updateCategory: async (
    id: string,
    categoryData: { name?: string; description?: string }
  ): Promise<Category> => {
    const { data, error } = await supabase
      .from('categories')
      .update(categoryData)
      .eq('id', id)
      .select()
      .single()

    if (error) {
      throw new Error(error.message)
    }

    return data
  },

  // Delete category
  deleteCategory: async (id: string): Promise<void> => {
    const { error } = await supabase.from('categories').delete().eq('id', id)

    if (error) {
      throw new Error(error.message)
    }
  },
}
