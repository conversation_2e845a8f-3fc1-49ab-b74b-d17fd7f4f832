'use client'

import { useState, useEffect } from 'react'
import { ProtectedRoute } from '@/components/auth/ProtectedRoute'
import { Button } from '@/components/ui/Button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card'
import { SimpleBar<PERSON>hart, SimplePieChart, SimpleLine<PERSON>hart } from '@/components/charts/SimpleChart'
import { analyticsService } from '@/lib/services/analyticsService'
import { formatCurrency } from '@/utils'
import type {
  DashboardStats,
  SalesAnalytics,
  CustomerAnalytics,
  InventoryAnalytics,
} from '@/lib/services/analyticsService'

function ReportsContent() {
  const [stats, setStats] = useState<DashboardStats | null>(null)
  const [salesAnalytics, setSalesAnalytics] = useState<SalesAnalytics | null>(null)
  const [customerAnalytics, setCustomerAnalytics] = useState<CustomerAnalytics | null>(null)
  const [inventoryAnalytics, setInventoryAnalytics] = useState<InventoryAnalytics | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [dateRange, setDateRange] = useState({
    startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    endDate: new Date().toISOString().split('T')[0],
  })

  useEffect(() => {
    loadReportsData()
  }, [])

  const loadReportsData = async () => {
    try {
      setLoading(true)
      const [dashboardStats, salesData, customerData, inventoryData] = await Promise.all([
        analyticsService.getDashboardStats(),
        analyticsService.getSalesAnalytics(30),
        analyticsService.getCustomerAnalytics(),
        analyticsService.getInventoryAnalytics(),
      ])

      setStats(dashboardStats)
      setSalesAnalytics(salesData)
      setCustomerAnalytics(customerData)
      setInventoryAnalytics(inventoryData)
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to load reports data')
      console.error('Reports data loading error:', error)
    } finally {
      setLoading(false)
    }
  }

  const generateReport = async () => {
    try {
      setLoading(true)
      const report = await analyticsService.generateBusinessReport(
        dateRange.startDate,
        dateRange.endDate
      )

      // Create downloadable report
      const reportData = JSON.stringify(report, null, 2)
      const blob = new Blob([reportData], { type: 'application/json' })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `business-report-${dateRange.startDate}-to-${dateRange.endDate}.json`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to generate report')
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <div className='container mx-auto px-4 py-8'>
        <div className='flex items-center justify-center min-h-64'>
          <div className='animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600' />
          <span className='ml-2 text-gray-500'>Loading reports...</span>
        </div>
      </div>
    )
  }

  return (
    <div className='container mx-auto px-4 py-8'>
      <div className='flex justify-between items-center mb-8'>
        <div>
          <h1 className='text-3xl font-bold text-gray-900'>Business Reports & Analytics</h1>
          <p className='text-gray-600 mt-2'>Comprehensive insights into your store performance</p>
        </div>
        <div className='flex space-x-4'>
          <Button variant='outline' onClick={loadReportsData}>
            Refresh Data
          </Button>
          <Button onClick={generateReport}>Generate Report</Button>
        </div>
      </div>

      {error && (
        <div className='bg-red-50 border border-red-200 rounded-md p-4 mb-6'>
          <p className='text-sm text-red-600'>{error}</p>
        </div>
      )}

      {/* Date Range Selector */}
      <Card className='mb-8'>
        <CardHeader>
          <CardTitle>Report Period</CardTitle>
          <CardDescription>Select date range for detailed analysis</CardDescription>
        </CardHeader>
        <CardContent>
          <div className='grid grid-cols-1 md:grid-cols-3 gap-4 items-end'>
            <div>
              <label className='block text-sm font-medium text-gray-700 mb-1'>Start Date</label>
              <input
                type='date'
                value={dateRange.startDate}
                onChange={(e) => setDateRange((prev) => ({ ...prev, startDate: e.target.value }))}
                className='w-full h-10 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'
              />
            </div>
            <div>
              <label className='block text-sm font-medium text-gray-700 mb-1'>End Date</label>
              <input
                type='date'
                value={dateRange.endDate}
                onChange={(e) => setDateRange((prev) => ({ ...prev, endDate: e.target.value }))}
                className='w-full h-10 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'
              />
            </div>
            <Button onClick={loadReportsData}>Update Reports</Button>
          </div>
        </CardContent>
      </Card>

      {/* Executive Summary */}
      {stats && (
        <Card className='mb-8'>
          <CardHeader>
            <CardTitle>Executive Summary</CardTitle>
            <CardDescription>Key performance indicators at a glance</CardDescription>
          </CardHeader>
          <CardContent>
            <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6'>
              <div className='text-center'>
                <div className='text-3xl font-bold text-blue-600'>
                  {formatCurrency(stats.total_payments_amount, 'PHP')}
                </div>
                <div className='text-sm text-gray-500'>Total Revenue</div>
              </div>
              <div className='text-center'>
                <div className='text-3xl font-bold text-green-600'>
                  {stats.payment_rate.toFixed(1)}%
                </div>
                <div className='text-sm text-gray-500'>Payment Rate</div>
              </div>
              <div className='text-center'>
                <div className='text-3xl font-bold text-purple-600'>{stats.active_customers}</div>
                <div className='text-sm text-gray-500'>Active Customers</div>
              </div>
              <div className='text-center'>
                <div className='text-3xl font-bold text-orange-600'>{stats.active_products}</div>
                <div className='text-sm text-gray-500'>Active Products</div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Sales Analytics */}
      <div className='grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8'>
        {salesAnalytics && (
          <>
            <SimpleLineChart
              data={salesAnalytics.daily_sales.map((day) => ({
                label: new Date(day.date).toLocaleDateString('en-US', {
                  month: 'short',
                  day: 'numeric',
                }),
                value: day.total_amount,
              }))}
              title='Daily Sales Trend'
              formatValue={(value) => formatCurrency(value, 'PHP')}
              color='#3B82F6'
            />

            <SimpleBarChart
              data={salesAnalytics.monthly_sales.map((month) => ({
                label: new Date(month.month + '-01').toLocaleDateString('en-US', {
                  month: 'short',
                  year: 'numeric',
                }),
                value: month.total_amount,
              }))}
              title='Monthly Sales Performance'
              formatValue={(value) => formatCurrency(value, 'PHP')}
            />
          </>
        )}
      </div>

      {/* Product and Customer Analytics */}
      <div className='grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8'>
        {salesAnalytics && (
          <SimpleBarChart
            data={salesAnalytics.top_products.slice(0, 8).map((product) => ({
              label:
                product.product_name.length > 12
                  ? `${product.product_name.substring(0, 12)}...`
                  : product.product_name,
              value: product.total_quantity,
            }))}
            title='Top Products by Quantity Sold'
            formatValue={(value) => `${value} units`}
          />
        )}

        {customerAnalytics && (
          <SimpleBarChart
            data={customerAnalytics.top_debtors.slice(0, 8).map((debtor) => ({
              label:
                debtor.customer_name.length > 12
                  ? `${debtor.customer_name.substring(0, 12)}...`
                  : debtor.customer_name,
              value: debtor.total_debt,
            }))}
            title='Top Debtors'
            formatValue={(value) => formatCurrency(value, 'PHP')}
          />
        )}
      </div>

      {/* Inventory Analytics */}
      {inventoryAnalytics && (
        <div className='grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8'>
          <Card>
            <CardHeader>
              <CardTitle>Stock Status Overview</CardTitle>
              <CardDescription>Current inventory status by category</CardDescription>
            </CardHeader>
            <CardContent>
              <div className='space-y-4'>
                {['critical', 'low', 'normal', 'high'].map((status) => {
                  const count = inventoryAnalytics.stock_levels.filter(
                    (item) => item.status === status
                  ).length
                  const color = {
                    critical: 'bg-red-500',
                    low: 'bg-yellow-500',
                    normal: 'bg-blue-500',
                    high: 'bg-green-500',
                  }[status]

                  return (
                    <div key={status} className='flex items-center justify-between'>
                      <div className='flex items-center space-x-2'>
                        <div className={`w-3 h-3 rounded-full ${color}`} />
                        <span className='capitalize text-sm font-medium'>{status} Stock</span>
                      </div>
                      <span className='text-sm font-bold'>{count} items</span>
                    </div>
                  )
                })}
              </div>
            </CardContent>
          </Card>

          <SimpleBarChart
            data={inventoryAnalytics.category_performance.map((category) => ({
              label:
                category.category_name.length > 12
                  ? `${category.category_name.substring(0, 12)}...`
                  : category.category_name,
              value: category.total_stock_value,
            }))}
            title='Inventory Value by Category'
            formatValue={(value) => formatCurrency(value, 'PHP')}
          />
        </div>
      )}

      {/* Payment Methods and Customer Segments */}
      <div className='grid grid-cols-1 lg:grid-cols-2 gap-6'>
        {salesAnalytics && (
          <SimplePieChart
            data={salesAnalytics.payment_methods.map((method) => ({
              label: method.method.replace('_', ' ').toUpperCase(),
              value: method.total_amount,
            }))}
            title='Revenue by Payment Method'
            formatValue={(value) => formatCurrency(value, 'PHP')}
          />
        )}

        {customerAnalytics && (
          <SimplePieChart
            data={[
              {
                label: 'No Debt',
                value: customerAnalytics.customer_segments.no_debt,
                color: '#10B981',
              },
              {
                label: 'Low Debt (≤₱500)',
                value: customerAnalytics.customer_segments.low_debt,
                color: '#F59E0B',
              },
              {
                label: 'Medium Debt (₱501-₱2000)',
                value: customerAnalytics.customer_segments.medium_debt,
                color: '#EF4444',
              },
              {
                label: 'High Debt (>₱2000)',
                value: customerAnalytics.customer_segments.high_debt,
                color: '#7C2D12',
              },
            ]}
            title='Customer Debt Distribution'
            formatValue={(value) => `${value} customers`}
          />
        )}
      </div>
    </div>
  )
}

export default function ReportsPage() {
  return (
    <ProtectedRoute requiredPermission='view_analytics'>
      <ReportsContent />
    </ProtectedRoute>
  )
}
